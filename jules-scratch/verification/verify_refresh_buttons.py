from playwright.sync_api import sync_playwright, expect
import time

def run(playwright):
    browser = playwright.chromium.launch(headless=True)
    context = browser.new_context()
    page = context.new_page()

    # Listen for all console events and print them
    page.on("console", lambda msg: print(f"BROWSER CONSOLE: {msg.text}"))

    try:
        page.goto("http://localhost:3000", timeout=60000)

        # Upload a file
        file_path = 'backend/test_data/Addo_Elephant_1Page.pdf'
        page.locator('input[type="file"]').set_input_files(file_path)

        # Click the analyze button
        page.get_by_role("button", name="Analyze Document").click()

        # Wait for either the results to appear or an error message
        error_locator = page.locator(".bg-red-100")
        sections_locator = page.get_by_text("Document Sections")

        # Wait up to 120 seconds for either the sections or an error to appear
        for _ in range(120):
            if sections_locator.is_visible() or error_locator.is_visible():
                break
            time.sleep(1)

        if error_locator.is_visible():
            error_text = error_locator.inner_text()
            print(f"Error message found on page: {error_text}")
            page.screenshot(path="jules-scratch/verification/error.png")
            # Fail the test explicitly if an error is found
            raise AssertionError(f"Analysis failed with an error message on the page: {error_text}")

        # If no error was found, expect the sections to be visible and take screenshot
        expect(sections_locator).to_be_visible()
        page.screenshot(path="jules-scratch/verification/refresh_buttons.png")

    finally:
        browser.close()

with sync_playwright() as playwright:
    run(playwright)
