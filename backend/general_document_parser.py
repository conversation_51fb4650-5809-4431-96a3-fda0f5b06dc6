#This file is for functions that will parse the entire document. Functions should recieve the full document and return only the answer to the question.
from helpers.LLM_Handler import LLM_Handler, openAIModels, geminiModels
from helpers.Document_Processing import Doc_Processor



#policy info to extract in future, per tourplan screenshots
# Supplier name
# Has Single, Twin, Double, Triple, Quad room layouts
# Age policy: What defines an infant (0-3 for example), child or adult?
# Stay rules: Stay may start on what day of the week? Stay must include what days of the week?
# Cross season policy - first rate, average rate, split rate, not allowed
# "Internet: for B2B and extranet perpose" - does the agent have internet access, is there a URL for more info, etc.
class general_document_parser:

    def __init__(self, doc, model="gpt-4o-mini"):
        """
        Initialize the document processor with the specified model.

        Args:
            doc (str): The document to check.
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
        self.doc = doc

    def get_valid(self):
        """
        Check if the document contains accommodation information.

        Returns:
            bool: True if the document contains accommodation information, False otherwise.
        """
        prompt = """Does the following document contain accommodation information, such as rooms for booking and their prices? Return only TRUE or FALSE."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip().upper()=="TRUE"
    
    def get_supplier_name(self, supplier_list):
        """
        Get the names of the supplier in the document and matches it to the known supplier list.

        Returns:
            supplier(str): The name of the supplier
        """
        prompt = f"""In the following document, a company providing travel-related services such as accommodation, car rental, or similar offerings will be mentioned. 
        Identify the name of the company or supplier and return only the core name, excluding any descriptive terms like "game reserve," "hotel," "lodge," or "resort." 

        Return the name from this list: {supplier_list} that is the closest to the name you identified, return **ONLY** the name from the list.

        If no company name is found, return only the word 'NONE'"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return [resp.strip() for resp in response.split("\n") if resp.strip()]

        # for extracted_name in extracted_names:
        #     match, score = process.extractOne(extracted_name, supplier_list)
        #     if score > 60:
        #         return match
        #return "NONE"

    def get_property_names(self):
        """
        Get the names of properties in the document.

        Returns:
            list: List of property names.
        """
        prompt = """ Role and Objective
- Extract the name(s) of accommodation properties from the provided document, focusing only on full and unique property names offering overnight stay.
# Instructions
- Identify and return the **name of each distinct accommodation property** found in the document.
- An accommodation property is an establishment offering overnight lodging (e.g., hotel, lodge, guest house, campsite, similar).
- Submit only the overall property name, never the types of individual rooms, suites, or units within a property.
## Sub-categories
- Exclude the following from your output:
- Names of on-site facilities such as restaurants, golf courses, spas, or any other amenities not providing overnight lodging.
- General headers or descriptive phrases (e.g., "Fancyland Accommodation") that do not refer to a specific property.
- Repeated entries, partial names, or non-unique mentions not clearly denoting a property.
- Resort sections, areas, or zones (e.g., "The Lakefront Area") that are not actual properties offering accommodation.
- For large resorts containing individually named properties (e.g., "Fancyland" resort with "The Royal Lodge" and "Sunset Cabins"), return **only** the actual named accommodation properties, **not** the umbrella resort name.
# Context
- Names may appear in any language or script; include them exactly as shown—do not translate, normalize, or alter the names.
- If the name of a property is ambiguous, unclear, or appears only as a part of a larger, undefined name, **do not include** it in the output.
# Verification
- After extraction, review each name to confirm it does not fall under the exclusion list and is not ambiguous or partial. If no valid property names remain, return only the word **NONE**.
# Output Format
- Return a plain text list: each valid property name is on its own line.
- If no valid property names are found, return only the word: **NONE** (all caps).
## Example Outputs
```
The Royal Lodge
Sunset Cabins
```
OR
```
NONE
```
# Verbosity
- Return only the exact property names or **NONE**, with no additional commentary.
# Stop Conditions
- Complete the extraction and formatting as specified; if output is unclear or no valid properties are present, return **NONE** and do not escalate or prompt for clarification."""
        
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        return [resp.strip() for resp in resplist if resp.strip()]
    
    def get_overarching_period(self, num_retries = 3):
        """
        Get the overarching period of validity in the document.

        Returns:
            list: List containing the start date and end date of the overarching period.
        """
        prompt = f"""In the following document which should contain accommodation information relating to South African accommodations, how long is the document/contract valid? 
        Return only the start date and then the end date on the following line. Format dates in ISO format, as follows: YYYY-MM-DD.  If you cannot find any dates, simply output NONE"""
        for attempt in range(num_retries):
            response = self.llm_handler.sendMessageToLLM(self.doc, prompt)
            resplist = response.split("\n")
            if len(resplist) >= 2 and resplist[0] and resplist[1]:
                break
        if response.upper()=="NONE" or not (len(resplist) >= 2 and resplist[0] and resplist[1]): return ["2020-01-01","2030-01-01"]
        return [resp.strip() for resp in resplist if resp.strip()]

    def get_periods(self):
        """
        Get the periods used to define seasonal price ranges in the document.

        Returns:
            list: List of periods.
        """
        prompt = """Begin with a concise checklist (3-7 bullets) of what you will do; keep items conceptual, not implementation-level. Review the provided accommodation document and extract the distinct periods used to define seasonal price ranges, such as "summer," "winter," "low season," "high season," or "peak season." Exclude periods described solely as "midweek" or "weekend"; include only periods that reference actual seasons or overall pricing distinctions.
If the document does not mention any named seasonal periods, identify the period of validity (for example, a date range like "10 Jan 2024 - 31 Dec 2024") and use it as the seasonal period. If both named periods (e.g., "Summer", "Winter") and date ranges are present, list only the named periods and disregard the date ranges.
After extraction, validate that only qualifying seasonal or overall periods are included, and that midweek/weekend-only labels are excluded. If no relevant periods are identified, ensure the array is empty.
## Output Format
Provide the extracted period names in the following JSON format:
{
"seasonal_periods": [
"Period 1",
"Period 2"
]
}
If no relevant periods are identified, return an empty array:
{
"seasonal_periods": []
}"""
        response = self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt, field_to_extract="seasonal_periods")
        print(response)
        if isinstance(response, list):
            #ensure distinct
            response = list(set(response))
        return response
    
    def general_has_sto(self):
        """
        Check if the document contains a distinction between STO Rate and Published Rate.

        Returns:
            bool: True if the distinction exists, False otherwise.
        """
        prompt = f"""In the following document which should contain accommodation information, is there an explicit distinction between a "STO Rate" and a "Published Rate" or "Rack Rate"?
        Return only TRUE if there is an explicitly mentioned STO Rate, or FALSE if there is not."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        has_sto = "TRUE" in response.upper()
        if not has_sto:
            # just do a double check, manually searching for the keyword
            if "sto rate" in self.doc.lower() or " sto " in self.doc.lower():
                has_sto = True
        return has_sto
    
    def general_has_distinct_week_rates(self):
        prompt = f"""Begin with a concise checklist (3-7 bullets) of what you will do; keep items conceptual, not implementation-level. Analyze the provided accommodation document to determine if there are distinct rates based on specific days of the week or duration of stay.
Instructions:
- Look for explicit mentions of rate periods such as "week rates", "weekday rates", "weekend rates", or similar terms indicating different pricing for various days of the week.
- Also search for mentions of "long stay", "extended stay", or similar wording that indicates separate pricing for longer durations.
- Ignore any seasonal pricing; focus solely on distinctions based on days of the week or length of stay (e.g., daily vs long stay rates).
Validation: After identifying candidate rate types, double-check that they match the day-based or duration-based distinctions described, and do not conflate with seasonal rates.
Output:
- If the document specifies distinct day-based or duration-based rates, return a JSON array of these rate types. Example: ["Weekdays", "Weekends"], ["Normal", "Long Stay"], ["Weekdays", "Weekends", "Long Stay"].
- If there are no explicit mentions of such rates, return an empty array [] or ["Normal"]. Use ["Normal"] if the document lists only a base or default rate.
- If only seasonal rates are listed (with no daily or duration-based breakout), treat it as if no specific day-to-day or long stay rates are present, and return [] or ["Normal"].
Output Format:
Return only a JSON array, e.g.:
["Weekdays", "Weekends"]
["Normal", "Long Stay"]
["Weekdays", "Weekends", "Long Stay"]
[]
["Normal"]"""
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt)
