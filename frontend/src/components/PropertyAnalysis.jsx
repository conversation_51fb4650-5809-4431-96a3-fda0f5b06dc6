import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import EnhancedPdfViewer from './EnhancedPdfViewer';
import { modelOptions } from '../modelOptions';


function PropertyAnalysis() {
    const navigate = useNavigate();
    const location = useLocation();
    // const [documentInfo, setDocumentInfo] = React.useState(null); // Seems unused, can be removed if not needed elsewhere
    // const [propertyList, setPropertyList] = React.useState([]); // Seems unused, can be removed if not needed elsewhere
    const [pdfUrl, setPdfUrl] = React.useState(null);
    const [pdfFile, setPdfFile] = React.useState(null); // State to hold the file object
    const [selectedProperty, setSelectedProperty] = React.useState(null);
    const [propertyAnalysis, setPropertyAnalysis] = React.useState({
        includes: [],
        excludes: [],
        validation: null,
        roomTypes: { room_types: [] },
        mealTypes: { meal_types: [] },
        periods: [],
        childPolicy: null,
        levies: null
    });
    const [endpointStatus, setEndpointStatus] = React.useState({});
    const [error, setError] = React.useState(null);
    const [selectedModel, setSelectedModel] = React.useState('gemini-2.0-flash');
    
    const [editingRoomTypes, setEditingRoomTypes] = React.useState(false);
    const [roomTypeList, setRoomTypeList] = React.useState([]);
    const [newRoomType, setNewRoomType] = React.useState('');

    const [editingPeriods, setEditingPeriods] = React.useState(false);
    const [periodList, setPeriodList] = React.useState([]);
    const [newPeriod, setNewPeriod] = React.useState({ name: '', start: '', end: '' });

    const [editingMealTypes, setEditingMealTypes] = React.useState(false);
    const [mealTypeList, setMealTypeList] = React.useState([]);
    const [newMealType, setNewMealType] = React.useState('');
    // Rates differ by meal type check
    const [ratesDifferLoading, setRatesDifferLoading] = React.useState(false);
    const [ratesDifferError, setRatesDifferError] = React.useState(null);
    const [ratesDifferDefault, setRatesDifferDefault] = React.useState(null); // value returned by API
    const [ratesDiffer, setRatesDiffer] = React.useState(null); // user adjustable

    const [editingIncludes, setEditingIncludes] = React.useState(false);
    const [includesList, setIncludesList] = React.useState([]);
    const [newInclude, setNewInclude] = React.useState('');

    const [editingExcludes, setEditingExcludes] = React.useState(false);
    const [excludesList, setExcludesList] = React.useState([]);
    const [newExclude, setNewExclude] = React.useState('');

    const [editingChildPolicy, setEditingChildPolicy] = React.useState(false);
    const [childPolicyList, setChildPolicyList] = React.useState([]);
    const [newChildPolicy, setNewChildPolicy] = React.useState({ min: '', max: '' });

    const [editingLevies, setEditingLevies] = React.useState(false);
    const [leviesList, setLeviesList] = React.useState([]);
    const [newLevy, setNewLevy] = React.useState({ type: '', percentage: '', unit: 'room_per_night', costs: [{ amount: '', description: '' }] });
    
    const [workflowState, setWorkflowState] = React.useState(null);

    // State for PDF search functionality
    const [searchText, setSearchText] = React.useState('');
    const [searchStatus, setSearchStatus] = React.useState(null); // e.g., 'searching', 'found', 'not_found'

    const storedBaseUrl = localStorage.getItem('baseUrl');
    const baseUrl = storedBaseUrl || 'http://localhost:6060';

    if (!storedBaseUrl) {
        localStorage.setItem('baseUrl', baseUrl);
    }

    const endpointMap = {
        validation: { url: '/validate-property', stateKey: 'validation', label: 'Property Validation' },
        periods: { url: '/check-periods', stateKey: 'periods', label: 'Periods' },
        roomTypes: { url: '/property-room-types', stateKey: 'roomTypes', label: 'Room Types' },
        mealTypes: { url: '/property-meal-types', stateKey: 'mealTypes', label: 'Meal Types' },
        childPolicy: { url: '/property-child-policy', stateKey: 'childPolicy', label: 'Child Policy' },
        levies: { url: '/property-levies', stateKey: 'levies', label: 'Levies' },
        includes: { url: '/property-includes', stateKey: 'includes', specialParsing: 'csv', label: 'Includes' },
        excludes: { url: '/property-excludes', stateKey: 'excludes', specialParsing: 'csv', label: 'Excludes' },
    };

    const handleRefresh = async (sectionName) => {
        if (!window.confirm("Refreshing this section will discard any unsaved changes. Are you sure?")) {
            return;
        }

        const endpoint = endpointMap[sectionName];
        if (!endpoint) {
            console.error("Invalid section name for refresh:", sectionName);
            return;
        }

        setEndpointStatus(prev => ({ ...prev, [sectionName]: { ...prev[sectionName], status: 'loading' } }));
        setError(null);

        try {
            const filename = workflowState.documentFilename;
            const property = selectedProperty;
            const currentModel = localStorage.getItem('selectedModel') || selectedModel;

            const requestBody = {
                filename,
                property_name: property,
                model: currentModel
            };

            if (sectionName === 'periods') {
                const periodsListForCheck = periodList; // Use the current state of periodList
                const startDate = workflowState.overarchingPeriod && workflowState.overarchingPeriod.start_date || null;
                const endDate = workflowState.overarchingPeriod && workflowState.overarchingPeriod.end_date || null;
                requestBody.periods = periodsListForCheck;
                requestBody.start_date = startDate;
                requestBody.end_date = endDate;
            }

            const response = await fetch(`${baseUrl}${endpoint.url}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to refresh ${sectionName}: ${errorText}`);
            }

            const data = await response.json();

            console.log(`Refreshed ${sectionName} data (${endpoint.stateKey}):`, data);

            const parseCsvString = (csvString) => {
                if (typeof csvString === 'string' && csvString.trim() !== '') {
                    return csvString.split(',').map(item => item.trim());
                }
                return [];
            };

            setPropertyAnalysis(prev => {
                const newState = { ...prev };
                if (endpoint.specialParsing === 'csv') {
                    newState[endpoint.stateKey] = parseCsvString(data[endpoint.stateKey]);
                } else if (endpoint.stateKey === 'periods' && data && Array.isArray(data.periods)) {
                    newState.periods = data.periods;
                } else {
                    newState[endpoint.stateKey] = data;
                }
                return newState;
            });
            setEndpointStatus(prev => ({ ...prev, [sectionName]: { ...prev[sectionName], status: 'completed' } }));

            // If we just refreshed mealTypes or roomTypes, re-check whether rates differ by meal type
            if (sectionName === 'mealTypes' || sectionName === 'roomTypes') {
                // Use the latest lists from state (these will be updated via effects that sync propertyAnalysis)
                try {
                    await fetchRatesDiffer();
                } catch (e) {
                    // fetchRatesDiffer handles setting its own error state
                }
            }

        } catch (err) {
            console.error('Refresh error:', err);
            setError(err.message);
            setEndpointStatus(prev => ({ ...prev, [sectionName]: { ...prev[sectionName], status: 'error', message: err.message } }));
        }
    };

    React.useEffect(() => {
        const savedWorkflowState = localStorage.getItem('workflowState');
        const receivedFile = location.state?.file;
        const savedModel = localStorage.getItem('selectedModel');

        if (savedModel) {
            console.log(`setting selected model to: ${savedModel}`);
            setSelectedModel(savedModel);
        } else {
            console.log(`No saved model. Leaving selected model as default`);
        }

        if (receivedFile) {
            setPdfFile(receivedFile); // Store the file object in state
            const fileUrl = URL.createObjectURL(receivedFile);
            setPdfUrl(fileUrl);
        } else {
            setError("PDF file not received. Please go back and try again.");
            return;
        }

        if (savedWorkflowState) {
            const parsedState = JSON.parse(savedWorkflowState);
            setWorkflowState(parsedState);

            if (parsedState.currentPropertyIndex >= 0 && parsedState.currentPropertyIndex < parsedState.properties.length) {
                const currentProperty = parsedState.properties[parsedState.currentPropertyIndex];
                setSelectedProperty(currentProperty);
                console.log(`Workflow: Analyzing property ${parsedState.currentPropertyIndex + 1} of ${parsedState.totalProperties}: ${currentProperty}`);
                fetchPropertyAnalysis(currentProperty, parsedState);
            } else {
                setError(`Invalid property index (${parsedState.currentPropertyIndex}) in workflow state.`);
                console.error("Invalid workflow state:", parsedState);
            }
        } else {
            setError("Workflow state not found in localStorage. Please start from the home page.");
        }
    }, [location.state]);

    const handlePropertyAnalysisBackNavigation = async () => {
        const cameFromLastStepFlag = localStorage.getItem('cameFromRoomTypeAnalysisLastStep');
        localStorage.removeItem('cameFromRoomTypeAnalysisLastStep'); // Remove flag immediately

        const currentPdfFile = pdfFile || location.state?.file; // Ensure pdfFile is available

        if (!currentPdfFile) {
            console.error("PDF file is missing. Navigating to home.");
            navigate('/'); // Navigate home if no PDF file
            return;
        }

        const savedWorkflowStateString = localStorage.getItem('workflowState');
        if (!savedWorkflowStateString) {
            console.warn("Workflow state not found. Navigating to home.");
            navigate('/', { state: { file: currentPdfFile } });
            return;
        }

        try {
            const currentWorkflowState = JSON.parse(savedWorkflowStateString);

            if (cameFromLastStepFlag === 'true' &&
                currentWorkflowState &&
                Array.isArray(currentWorkflowState.properties) &&
                typeof currentWorkflowState.currentPropertyIndex === 'number' &&
                currentWorkflowState.currentPropertyIndex > 0) {

                const previousPropertyIndex = currentWorkflowState.currentPropertyIndex - 1;
                if (previousPropertyIndex < 0 || previousPropertyIndex >= currentWorkflowState.properties.length) {
                    console.error("Previous property index out of bounds.");
                    navigate('/', { state: { file: currentPdfFile } });
                    return;
                }
                const previousPropertyName = currentWorkflowState.properties[previousPropertyIndex];
                const docFilename = currentWorkflowState.documentFilename;
                const modelToUse = currentWorkflowState.model || selectedModel; // Prioritize model from workflowState

                if (!docFilename || !previousPropertyName) {
                    console.error("Document filename or previous property name is missing from workflow state.");
                    navigate('/', { state: { file: currentPdfFile } });
                    return;
                }

                setError(null);

                try {
                    const response = await fetch(`${baseUrl}/property-room-types`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            filename: docFilename,
                            property_name: previousPropertyName,
                            model: modelToUse
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to fetch room types for ${previousPropertyName}: ${errorText} (Status: ${response.status})`);
                    }

                    const roomTypesData = await response.json();
                    const previousPropertyRoomTypes = roomTypesData.room_types || [];

                    if (previousPropertyRoomTypes.length > 0) {
                        console.log(`Found ${previousPropertyRoomTypes.length} room types for previous property ${previousPropertyName}. Navigating to RoomTypeAnalysis.`);
                        const targetRoomTypeIndex = previousPropertyRoomTypes.length; // Go to the last room type

                        // NOTE: Fetching periods for the previous property is not done here.
                        // RoomTypeAnalysis will need to be robust or fetch its own periods if currentPropertyPeriods is not set.
                        // For now, we assume currentPropertyPeriods might be stale or RoomTypeAnalysis handles it.
                        // A more complete solution might involve fetching periods here as well or ensuring workflowState has them.
                        // For now, we are only ensuring room types and indices are set.

                        const updatedWorkflowState = {
                            ...currentWorkflowState,
                            currentPropertyIndex: previousPropertyIndex,
                            currentRoomTypeIndex: targetRoomTypeIndex,
                            currentPropertyRoomTypes: previousPropertyRoomTypes,
                            // currentPropertyPeriods: should be fetched or handled by RoomTypeAnalysis if needed for prev property
                        };
                        localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
                        navigate('/room-type-analysis', { state: { file: currentPdfFile } });
                    } else {
                        console.warn(`No room types found for previous property ${previousPropertyName}. Navigating to App.jsx`);
                        // Fallback: If no room types, or if we decide not to go to RoomTypeAnalysis for an empty list,
                        // we could navigate to PropertyAnalysis for that previous property instead,
                        // but that would require re-fetching its full details.
                        // For simplicity, current fallback is to App.jsx.
                        // Or, stay on PropertyAnalysis for the *previous* property by calling fetchPropertyAnalysis(previousPropertyName, updatedWorkflowState)
                        // and then navigating to self, but that's complex. Simplest is App.jsx.
                        navigate('/', { state: { file: currentPdfFile } });
                    }
                } catch (fetchError) {
                    console.error('Error during back navigation to previous property room type:', fetchError);
                    setError(`Error navigating back: ${fetchError.message}`); // Show error to user
                    // Decide on fallback navigation, e.g., stay or go to App.jsx
                    navigate('/', { state: { file: currentPdfFile } }); // Fallback on error
                }
            } else {
                // Flag not true, or it's the first property, or workflowState is invalid. Navigate to App.jsx.
                navigate('/', { state: { file: currentPdfFile } });
            }
        } catch (parseError) {
            console.error("Error parsing workflowState:", parseError);
            navigate('/', { state: { file: currentPdfFile } }); // Fallback on error
        }
    };

    const fetchPropertyAnalysis = async (property, workflowData) => {
        setError(null);
        const initialStatus = {};
        Object.keys(endpointMap).forEach(key => {
            initialStatus[key] = { status: 'loading', label: endpointMap[key].label };
        });
        setEndpointStatus(initialStatus);

        const currentModel = localStorage.getItem('selectedModel') || selectedModel;

        // Reset propertyAnalysis state
        setPropertyAnalysis({
            includes: [],
            excludes: [],
            validation: null,
            roomTypes: { room_types: [] },
            mealTypes: { meal_types: [] },
            periods: [],
            childPolicy: null,
            levies: null
        });

        try {
            const filename = workflowData.documentFilename;
            const periodsListForCheck = (workflowData.periods && Array.isArray(workflowData.periods.periods)) ? workflowData.periods.periods : [];
            const startDate = workflowData.overarchingPeriod && workflowData.overarchingPeriod.start_date || null;
            const endDate = workflowData.overarchingPeriod && workflowData.overarchingPeriod.end_date || null;

            const promises = Object.entries(endpointMap).map(async ([key, endpoint]) => {
                const requestBody = {
                    filename,
                    property_name: property,
                    model: currentModel
                };

                if (key === 'periods') {
                    requestBody.periods = periodsListForCheck;
                    requestBody.start_date = startDate;
                    requestBody.end_date = endDate;
                }

                try {
                    const response = await fetch(`${baseUrl}${endpoint.url}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to fetch ${endpoint.label}: ${errorText}`);
                    }

                    const data = await response.json();

                    const parseCsvString = (csvString) => {
                        if (typeof csvString === 'string' && csvString.trim() !== '') {
                            return csvString.split(',').map(item => item.trim());
                        }
                        return [];
                    };

                    setPropertyAnalysis(prev => {
                        const newState = { ...prev };
                        if (endpoint.specialParsing === 'csv') {
                            newState[endpoint.stateKey] = parseCsvString(data[endpoint.stateKey]);
                        } else if (endpoint.stateKey === 'periods' && data && Array.isArray(data.periods)) {
                            newState.periods = data.periods;
                        } else {
                            newState[endpoint.stateKey] = data;
                        }
                        return newState;
                    });

                    setEndpointStatus(prev => ({
                        ...prev,
                        [key]: { ...prev[key], status: 'completed' }
                    }));

                } catch (err) {
                    console.error(`Error fetching ${endpoint.label}:`, err);
                    setEndpointStatus(prev => ({
                        ...prev,
                        [key]: { ...prev[key], status: 'error', message: err.message }
                    }));
                }
            });

            await Promise.allSettled(promises);
            console.log("All property analysis fetches settled.");

        } catch (err) {
            // This top-level catch is for setup errors before fetches start.
            console.error('Full error:', err);
            setError(err.message);
            const errorStatus = {};
            Object.keys(endpointMap).forEach(key => {
                errorStatus[key] = { status: 'error', label: endpointMap[key].label, message: err.message };
            });
            setEndpointStatus(errorStatus);
        }
    };
    
    // Synchronize local list states with propertyAnalysis
    React.useEffect(() => {
        setPeriodList(Array.isArray(propertyAnalysis?.periods) ? propertyAnalysis.periods : []);
    }, [propertyAnalysis?.periods]);

    React.useEffect(() => {
        setRoomTypeList(Array.isArray(propertyAnalysis?.roomTypes?.room_types) ? propertyAnalysis.roomTypes.room_types : []);
    }, [propertyAnalysis?.roomTypes?.room_types]);

    React.useEffect(() => {
        setMealTypeList(Array.isArray(propertyAnalysis?.mealTypes?.meal_types) ? propertyAnalysis.mealTypes.meal_types : []);
    }, [propertyAnalysis?.mealTypes?.meal_types]);

    // Fetch whether rates differ based on meal type from backend
    const fetchRatesDiffer = async () => {
        setRatesDifferLoading(true);
        setRatesDifferError(null);

        try {
            const filename = workflowState?.documentFilename;
            const property = selectedProperty;

            const modelToUse = workflowState.model || selectedModel; // Prioritize model from workflowState

            if (!filename || !property) {
                throw new Error('Missing filename or property for rates-differ check');
            }

            const requestBody = {
                filename,
                property_name: property,
                room_types: roomTypeList,
                meal_types: mealTypeList,
                model: modelToUse
            };

            const resp = await fetch(`${baseUrl}/has-differing-rates-depending-on-meal-type`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!resp.ok) {
                const txt = await resp.text();
                throw new Error(`Rates differ check failed: ${txt}`);
            }

            const json = await resp.json();
            const val = !!json?.has_differing_rates;
            setRatesDifferDefault(val);
            // Only set the user-adjustable value if it's not already set by user
            setRatesDiffer(prev => (prev === null ? val : prev));
        } catch (err) {
            console.error('Rates differ fetch error:', err);
            setRatesDifferError(err.message);
        } finally {
            setRatesDifferLoading(false);
        }
    };

    // When room types or meal types change (from backend or user edits), re-run the check
    React.useEffect(() => {
        // Only attempt after workflowState and selectedProperty are set
        if (!workflowState || !selectedProperty) return;
        // Debounce-ish: call when lists have settled
        fetchRatesDiffer();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [roomTypeList, mealTypeList, workflowState, selectedProperty]);

    React.useEffect(() => {
        setIncludesList(Array.isArray(propertyAnalysis?.includes) ? propertyAnalysis.includes : []);
    }, [propertyAnalysis?.includes]);

    React.useEffect(() => {
        setExcludesList(Array.isArray(propertyAnalysis?.excludes) ? propertyAnalysis.excludes : []);
    }, [propertyAnalysis?.excludes]);

    React.useEffect(() => {
        setChildPolicyList(Array.isArray(propertyAnalysis?.childPolicy?.child_policy) ? propertyAnalysis.childPolicy.child_policy : []);
    }, [propertyAnalysis?.childPolicy?.child_policy]);

    React.useEffect(() => {
        setLeviesList(Array.isArray(propertyAnalysis?.levies?.levies) ? propertyAnalysis.levies.levies : []);
    }, [propertyAnalysis?.levies?.levies]);


    // Handle room type editing
    const handleRoomTypeEdit = () => setEditingRoomTypes(true);
    const handleRoomTypeSave = () => {
        setEditingRoomTypes(false);
        setPropertyAnalysis(prevAnalysis => ({
            ...prevAnalysis,
            roomTypes: {
                ...prevAnalysis.roomTypes, // Preserve other potential properties in roomTypes
                room_types: roomTypeList
            }
        }));
    };
    const handleRoomTypeAdd = () => {
        if (newRoomType.trim() && !roomTypeList.includes(newRoomType.trim())) {
            setRoomTypeList([...roomTypeList, newRoomType.trim()]);
            setNewRoomType('');
        }
    };
    const handleRoomTypeRemove = (roomTypeToRemove) => {
        setRoomTypeList(roomTypeList.filter(roomType => roomType !== roomTypeToRemove));
    };
    const handleRoomTypeKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleRoomTypeAdd();
        }
    };

    // Handle period editing
    const handlePeriodEdit = () => setEditingPeriods(true);
    const handlePeriodSave = () => {
        setEditingPeriods(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            periods: periodList
        }));
    };
    const handlePeriodAdd = () => {
        if (
            newPeriod.name.trim() &&
            newPeriod.start && // Date inputs provide valueAsDate or value
            newPeriod.end &&
            !periodList.some(
                p =>
                    Array.isArray(p) && // Ensure p is an array
                    p[0] === newPeriod.name.trim() &&
                    p[1] === newPeriod.start &&
                    p[2] === newPeriod.end
            )
        ) {
            setPeriodList([...periodList, [newPeriod.name.trim(), newPeriod.start, newPeriod.end]]);
            setNewPeriod({ name: '', start: '', end: '' });
        }
    };
    const handlePeriodRemove = (index) => {
        setPeriodList(periodList.filter((_, i) => i !== index));
    };

    // Handle meal type editing
    const handleMealTypeEdit = () => setEditingMealTypes(true);
    const handleMealTypeSave = () => {
        setEditingMealTypes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            mealTypes: { 
                ...prev.mealTypes,
                meal_types: mealTypeList 
            }
        }));
    };
    const handleMealTypeAdd = () => {
        if (newMealType.trim() && !mealTypeList.includes(newMealType.trim())) {
            setMealTypeList([...mealTypeList, newMealType.trim()]);
            setNewMealType('');
        }
    };
    const handleMealTypeRemove = (item) => setMealTypeList(mealTypeList.filter(m => m !== item));
    const handleMealTypeKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleMealTypeAdd();
        }
    };

    // Handle includes editing
    const handleIncludesEdit = () => setEditingIncludes(true);
    const handleIncludesSave = () => {
        setEditingIncludes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            includes: includesList
        }));
    };
    const handleIncludesAdd = () => {
        if (newInclude.trim() && !includesList.includes(newInclude.trim())) {
            setIncludesList([...includesList, newInclude.trim()]);
            setNewInclude('');
        }
    };
    const handleIncludesRemove = (item) => setIncludesList(includesList.filter(i => i !== item));
    const handleIncludesKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleIncludesAdd();
        }
    };

    // Handle excludes editing
    const handleExcludesEdit = () => setEditingExcludes(true);
    const handleExcludesSave = () => {
        setEditingExcludes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            excludes: excludesList
        }));
    };
    const handleExcludesAdd = () => {
        if (newExclude.trim() && !excludesList.includes(newExclude.trim())) {
            setExcludesList([...excludesList, newExclude.trim()]);
            setNewExclude('');
        }
    };
    const handleExcludesRemove = (item) => setExcludesList(excludesList.filter(i => i !== item));
    const handleExcludesKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleExcludesAdd();
        }
    };

    // Handle child policy editing
    const handleChildPolicyEdit = () => setEditingChildPolicy(true);
    const handleChildPolicySave = () => {
        setEditingChildPolicy(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            childPolicy: { 
                ...prev.childPolicy,
                child_policy: childPolicyList 
            }
        }));
    };
    const handleChildPolicyAdd = () => {
        if (
            newChildPolicy.min.trim() !== '' && // Ensure min is not just spaces
            newChildPolicy.max.trim() !== '' && // Ensure max is not just spaces
            !childPolicyList.some(
                range => 
                    Array.isArray(range) && // Ensure range is an array
                    range[0] === newChildPolicy.min.trim() && 
                    range[1] === newChildPolicy.max.trim()
            )
        ) {
            setChildPolicyList([...childPolicyList, [newChildPolicy.min.trim(), newChildPolicy.max.trim()]]);
            setNewChildPolicy({ min: '', max: '' });
        }
    };
    const handleChildPolicyRemove = (index) => setChildPolicyList(childPolicyList.filter((_, i) => i !== index));

    // Handle levies editing
    const handleLeviesEdit = () => setEditingLevies(true);
    const handleLeviesSave = () => {
        setEditingLevies(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            levies: { 
                ...prev.levies,
                levies: leviesList 
            }
        }));
    };
    const handleLeviesAdd = () => {
        if (newLevy.type.trim() && (newLevy.percentage.toString().trim() !== '' || newLevy.costs.some(c => c.amount.toString().trim() !== ''))) {
            const levyToAdd = {
                ...newLevy,
                type: newLevy.type.trim(),
                unit: newLevy.unit || 'room_per_night',
                costs: newLevy.costs.filter(c => c.amount.toString().trim() !== '' || c.description.trim() !== '')
            };
            setLeviesList([...leviesList, levyToAdd]);
            setNewLevy({ type: '', percentage: '', unit: 'room_per_night', costs: [{ amount: '', description: '' }] });
        }
    };

    const handleNewLevyCostChange = (index, field, value) => {
        const updatedCosts = [...newLevy.costs];
        updatedCosts[index][field] = value;
        setNewLevy({ ...newLevy, costs: updatedCosts });
    };

    const addNewCostFieldToNewLevy = () => {
        setNewLevy({ ...newLevy, costs: [...newLevy.costs, { amount: '', description: '' }] });
    };

    const removeCostFieldFromNewLevy = (index) => {
        const updatedCosts = newLevy.costs.filter((_, i) => i !== index);
        setNewLevy({ ...newLevy, costs: updatedCosts });
    };

    const handleLeviesRemove = (index) => setLeviesList(leviesList.filter((_, i) => i !== index));

    // Change handlers for direct updating of values
    const handleRoomTypeChange = (index, value) => {
        const updatedRoomTypes = [...roomTypeList];
        updatedRoomTypes[index] = value;
        setRoomTypeList(updatedRoomTypes);
    };

    const handlePeriodChange = (index, field, value) => {
        const updatedPeriods = [...periodList];
        if (field === 'name') {
            updatedPeriods[index] = [value, updatedPeriods[index][1], updatedPeriods[index][2]];
        } else if (field === 'start') {
            updatedPeriods[index] = [updatedPeriods[index][0], value, updatedPeriods[index][2]];
        } else if (field === 'end') {
            updatedPeriods[index] = [updatedPeriods[index][0], updatedPeriods[index][1], value];
        }
        setPeriodList(updatedPeriods);
    };

    const handleMealTypeChange = (index, value) => {
        const updatedMealTypes = [...mealTypeList];
        updatedMealTypes[index] = value;
        setMealTypeList(updatedMealTypes);
    };

    const handleIncludeChange = (index, value) => {
        const updatedIncludes = [...includesList];
        updatedIncludes[index] = value;
        setIncludesList(updatedIncludes);
    };

    const handleExcludeChange = (index, value) => {
        const updatedExcludes = [...excludesList];
        updatedExcludes[index] = value;
        setExcludesList(updatedExcludes);
    };

    const handleChildPolicyChange = (index, field, value) => {
        const updatedChildPolicy = [...childPolicyList];
        if (field === 'min') {
            updatedChildPolicy[index] = [value, updatedChildPolicy[index][1]];
        } else if (field === 'max') {
            updatedChildPolicy[index] = [updatedChildPolicy[index][0], value];
        }
        setChildPolicyList(updatedChildPolicy);
    };

    const handleLevyChange = (levyIndex, field, value) => {
        const updatedLevies = [...leviesList];
        updatedLevies[levyIndex] = { ...updatedLevies[levyIndex], [field]: value };
        setLeviesList(updatedLevies);
    };

    const handleLevyCostChange = (levyIndex, costIndex, field, value) => {
        const updatedLevies = [...leviesList];
        const updatedCosts = [...updatedLevies[levyIndex].costs];
        updatedCosts[costIndex] = { ...updatedCosts[costIndex], [field]: value };
        updatedLevies[levyIndex].costs = updatedCosts;
        setLeviesList(updatedLevies);
    };

    const addCostToLevy = (levyIndex) => {
        const updatedLevies = [...leviesList];
        updatedLevies[levyIndex].costs.push({ amount: '', description: '' });
        setLeviesList(updatedLevies);
    };

    const removeCostFromLevy = (levyIndex, costIndex) => {
        const updatedLevies = [...leviesList];
        updatedLevies[levyIndex].costs = updatedLevies[levyIndex].costs.filter((_, i) => i !== costIndex);
        setLeviesList(updatedLevies);
    };

    // Function to map complex search terms to more effective search terms
    const mapSearchTerm = (itemText) => {
        if (!itemText || typeof itemText !== 'string') {
            return itemText;
        }

        const text = itemText.trim();

        // Handle section headers
        if (text === 'Meal Types') return 'meal';
        if (text === 'Child Policy') return 'child';
        if (text === 'Periods') return 'period';

        // Handle meal type mappings - extract key terms
        if (text.includes('B&B') || text.includes('Bed & Breakfast')) return 'breakfast';
        if (text.includes('HB') || text.includes('Half Board')) return 'half board';
        if (text.includes('FB') || text.includes('Full Board')) return 'full board';
        if (text.includes('AI') || text.includes('All-Inclusive') || text.includes('All Inclusive')) return 'all inclusive';
        if (text.includes('RO') || text.includes('Room Only')) return 'room only';
        if (text.includes('SC') || text.includes('Self Catering')) return 'self catering';

        // Handle child age range labels - convert to simple "child"
        if (text.match(/^Child Age Range \d+:?$/i)) return 'child';

        // Handle individual age numbers in child policy context
        // If it's a single digit or small number that could be an age, and we're in child policy context
        if (text.match(/^\d{1,2}$/) && parseInt(text) <= 18) {
            // For single age numbers, search for the full range if we can determine it
            // This is a simplified approach - in a real implementation, you might want to
            // track context to know which range this age belongs to
            return 'child';
        }

        // Handle age range separators and suffixes
        if (text === '-' || text === 'years') return 'child';

        // Handle complex child age range patterns - extract the full range
        const ageRangeMatch = text.match(/(\d+)\s*-\s*(\d+)\s*years?/i);
        if (ageRangeMatch) {
            return `${ageRangeMatch[1]} - ${ageRangeMatch[2]}`;
        }

        // Handle percentage labels and values
        if (text.includes('Percentage') || text.includes('%')) {
            return text; // Keep percentage searches as-is for now
        }

        // Handle cost/levy related terms
        if (text.includes('Cost') || text.includes('Levy') || text.includes('Fee')) {
            return text; // Keep cost searches as-is for now
        }

        // For very short terms that might be too broad, check if they're likely ages
        if (text.length <= 2 && text.match(/^\d+$/) && parseInt(text) <= 18) {
            return 'child'; // Single digits in this context are likely ages
        }

        // Default: return the original text
        return text;
    };

    // Handlers for PDF search functionality
    const handleItemClick = (itemText) => {
        const mappedSearchTerm = mapSearchTerm(itemText);
        setSearchText(mappedSearchTerm);
        setSearchStatus('searching'); // Indicate that viewer should start searching
    };

    const handleSearchResult = (status) => {
        setSearchStatus(status); // Update status based on search result from viewer (e.g., 'found', 'not_found')
    };

    const handleRoomTypeAnalysis = () => {
        if (!workflowState) {
            setError("Workflow state is not available. Cannot proceed.");
            return;
        }

        // Use the most up-to-date lists from their respective states
        const updatedWorkflowState = {
            ...workflowState,
            currentPropertyRoomTypes: roomTypeList, // Use roomTypeList state
            currentPropertyPeriods: periodList     // Use periodList state
        };

        try {
            // Persist the rates-differ choice (user-adjusted or default) so downstream steps can use it
            const ratesDifferValue = (ratesDiffer !== null) ? ratesDiffer : ratesDifferDefault;
            if (ratesDifferValue !== null) {
                updatedWorkflowState.ratesDifferBasedOnMealType = ratesDifferValue;
                localStorage.setItem('ratesDifferBasedOnMealType', JSON.stringify(ratesDifferValue));
            }
            localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
            // Use the local list states for localStorage as well
            localStorage.setItem('includes', JSON.stringify(includesList));
            localStorage.setItem('excludes', JSON.stringify(excludesList));
            localStorage.setItem('childAgeRanges', JSON.stringify(childPolicyList));
            localStorage.setItem('mealBasis', JSON.stringify(mealTypeList));

            console.log(`Workflow: Proceeding to Room Type analysis for property: ${selectedProperty}`);
            navigate('/room-type-analysis', { state: { file: pdfFile } });
        } catch (error) {
            setError("Failed to save workflow state to localStorage before navigating.");
            console.error("localStorage error:", error);
        }
    };
    
    // Helper to format date strings for display, handling invalid dates
    const formatDateString = (dateStr) => {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        // Check if date is valid; date objects from invalid strings will result in "Invalid Date"
        // getTime() on an invalid date returns NaN
        if (isNaN(date.getTime())) {
            return 'Invalid Date';
        }
        // Adjust for timezone issues if dateStr is like "YYYY-MM-DD"
        // by creating date in UTC to avoid off-by-one day errors.
        const [year, month, day] = dateStr.split('-').map(Number);
        if (year && month && day) {
            const utcDate = new Date(Date.UTC(year, month - 1, day));
            // Format as dd/mm/yyyy
            const formattedDay = String(utcDate.getUTCDate()).padStart(2, '0');
            const formattedMonth = String(utcDate.getUTCMonth() + 1).padStart(2, '0');
            const formattedYear = utcDate.getUTCFullYear();
            return `${formattedDay}/${formattedMonth}/${formattedYear}`;
        }
        // Fallback for other formats - format as dd/mm/yyyy
        const formattedDay = String(date.getDate()).padStart(2, '0');
        const formattedMonth = String(date.getMonth() + 1).padStart(2, '0');
        const formattedYear = date.getFullYear();
        return `${formattedDay}/${formattedMonth}/${formattedYear}`;
    };

    // Helper function to format levy unit display
    const formatLevyUnit = (unit) => {
        if (!unit || typeof unit !== 'string') return '';
        return unit.replace(/_/g, ' ');
    };

    // Helper to render search status icons
    const renderSearchStatusIcon = (itemText) => {
        if (searchText === itemText) {
            if (searchStatus === 'searching') {
                return <span className="ml-2 animate-spin">⏳</span>; // Spinner
            } else if (searchStatus === 'found') {
                return <span className="ml-2 text-green-500">✔️</span>; // Checkmark
            } else if (searchStatus === 'not_found') {
                return <span className="ml-2 text-red-500">❌</span>; // Cross
            }
        }
        return null;
    };

    const StatusIndicator = ({ status }) => {
        if (status === 'pending' || status === 'loading') {
            return (
                <div className="animate-pulse flex items-center">
                    <div className={`h-3 w-3 rounded-full ${status === 'pending' ? 'bg-gray-300' : 'bg-blue-500'} mr-2`}></div>
                    <span className="text-sm text-gray-500">{status === 'pending' ? 'Waiting...' : 'Loading...'}</span>
                </div>
            );
        } else if (status === 'completed') {
            return (
                <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm text-green-600">Completed</span>
                </div>
            );
        } else { // error
            return (
                <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm text-red-600">Failed</span>
                </div>
            );
        }
    };

    const renderAnalysisSection = (endpointName) => {
        console.log("Rendering completed section:", endpointName);
        switch (endpointName) {
            case 'validation':
                // We will handle validation differently as it's a top-level status.
                // This section can be used for a more detailed validation report if needed in the future.
                return null;
            case 'periods':
                return (
                    <div className="bg-indigo-50 p-4 rounded-lg">
                        {editingPeriods ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {periodList.map((period, index) => (
                                        <div key={index} className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <input
                                                    type="text"
                                                    value={period[0] || ''}
                                                    onChange={(e) => handlePeriodChange(index, 'name', e.target.value)}
                                                    className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                    placeholder="Period name"
                                                />
                                                <button onClick={() => handlePeriodRemove(index)} className="text-red-500 hover:text-red-700">×</button>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <input
                                                    type="date"
                                                    value={period[1] || ''}
                                                    onChange={(e) => handlePeriodChange(index, 'start', e.target.value)}
                                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                />
                                                <span className="text-gray-500 text-sm">to</span>
                                                <input
                                                    type="date"
                                                    value={period[2] || ''}
                                                    onChange={(e) => handlePeriodChange(index, 'end', e.target.value)}
                                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-2 items-end">
                                    <input
                                        type="text"
                                        value={newPeriod.name}
                                        onChange={e => setNewPeriod({ ...newPeriod, name: e.target.value })}
                                        placeholder="Period Name"
                                        className="px-3 py-2 border rounded-lg w-full"
                                    />
                                    <input
                                        type="date"
                                        value={newPeriod.start}
                                        onChange={e => setNewPeriod({ ...newPeriod, start: e.target.value })}
                                        className="px-3 py-2 border rounded-lg w-full"
                                    />
                                    <input
                                        type="date"
                                        value={newPeriod.end}
                                        onChange={e => setNewPeriod({ ...newPeriod, end: e.target.value })}
                                        className="px-3 py-2 border rounded-lg w-full"
                                    />
                                     <button
                                        onClick={handlePeriodAdd}
                                        className="sm:col-start-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 h-full"
                                    >
                                        Add
                                    </button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => { setEditingPeriods(false); setNewPeriod({ name: '', start: '', end: '' }); setPeriodList(Array.isArray(propertyAnalysis?.periods) ? propertyAnalysis.periods : []); }} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handlePeriodSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {periodList && periodList.length > 0 ? (
                                    <div>
                                        <div className="space-y-4">
                                            <div className="grid grid-cols-3 gap-4 font-semibold text-indigo-700">
                                                <div onClick={() => handleItemClick('Period Name')} className="cursor-pointer hover:underline">Period Name</div>
                                                <div onClick={() => handleItemClick('Start Date')} className="cursor-pointer hover:underline">Start Date</div>
                                                <div onClick={() => handleItemClick('End Date')} className="cursor-pointer hover:underline">End Date</div>
                                            </div>
                                            {periodList.map((period, index) => {
                                                const periodName = Array.isArray(period) ? period[0] : 'N/A';
                                                const startDate = Array.isArray(period) ? formatDateString(period[1]) : 'N/A';
                                                const endDate = Array.isArray(period) ? formatDateString(period[2]) : 'N/A';
                                                return (
                                                    <div key={index} className="grid grid-cols-3 gap-4 items-center">
                                                        <div>
                                                            <span onClick={() => handleItemClick(periodName)} className="cursor-pointer hover:underline">
                                                                {periodName}
                                                            </span>
                                                            {renderSearchStatusIcon(periodName)}
                                                        </div>
                                                        <div>
                                                            <span onClick={() => handleItemClick(startDate)} className="cursor-pointer hover:underline">
                                                                {startDate}
                                                            </span>
                                                            {renderSearchStatusIcon(startDate)}
                                                        </div>
                                                        <div>
                                                            <span onClick={() => handleItemClick(endDate)} className="cursor-pointer hover:underline">
                                                                {endDate}
                                                            </span>
                                                            {renderSearchStatusIcon(endDate)}
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                ) : ( <p className="text-gray-500">No periods found</p> )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('periods')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handlePeriodEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Periods</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'roomTypes':
                return (
                    <div className="bg-purple-50 p-4 rounded-lg">
                        {editingRoomTypes ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {roomTypeList.map((roomType, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={roomType}
                                                onChange={(e) => handleRoomTypeChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter room type"
                                            />
                                            <button onClick={() => handleRoomTypeRemove(roomType)} className="text-red-500 hover:text-red-700">×</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input type="text" value={newRoomType} onChange={(e) => setNewRoomType(e.target.value)} onKeyPress={handleRoomTypeKeyPress} placeholder="Add new room type" className="flex-grow px-3 py-2 border rounded-lg" />
                                    <button onClick={handleRoomTypeAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => {setEditingRoomTypes(false); setNewRoomType(''); setRoomTypeList(Array.isArray(propertyAnalysis?.roomTypes?.room_types) ? propertyAnalysis.roomTypes.room_types : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleRoomTypeSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {roomTypeList && roomTypeList.length > 0 ? (
                                    <div>
                                        <ul className="list-disc list-inside space-y-1">
                                            {roomTypeList.map((roomType, index) => (
                                                <li key={index} className="flex items-center">
                                                    <span onClick={() => handleItemClick(roomType)} className="cursor-pointer hover:underline">
                                                        {roomType}
                                                    </span>
                                                    {renderSearchStatusIcon(roomType)}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                ) : ( <p className="text-gray-500">No room types found</p> )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('roomTypes')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handleRoomTypeEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Room Types</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'mealTypes':
                return (
                    <div className="bg-orange-50 p-4 rounded-lg">
                        {editingMealTypes ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {mealTypeList.map((mealType, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={mealType}
                                                onChange={(e) => handleMealTypeChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter meal type"
                                            />
                                            <button onClick={() => handleMealTypeRemove(mealType)} className="text-red-500 hover:text-red-700">×</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input type="text" value={newMealType} onChange={e => setNewMealType(e.target.value)} onKeyPress={handleMealTypeKeyPress} placeholder="Add new meal type" className="flex-grow px-3 py-2 border rounded-lg" />
                                    <button onClick={handleMealTypeAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => {setEditingMealTypes(false); setNewMealType(''); setMealTypeList(Array.isArray(propertyAnalysis?.mealTypes?.meal_types) ? propertyAnalysis.mealTypes.meal_types : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleMealTypeSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {mealTypeList && mealTypeList.length > 0 ? (
                                    <div>
                                        <ul className="list-disc list-inside space-y-1">
                                            {mealTypeList.map((mealType, index) => (
                                                <li key={index} className="flex items-center">
                                                    <span onClick={() => handleItemClick(mealType)} className="cursor-pointer hover:underline">
                                                        {mealType}
                                                    </span>
                                                    {renderSearchStatusIcon(mealType)}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                ) : ( <p className="text-gray-500">No meal types found</p> )}
                                <div className="mt-4">
                                    <div className="flex items-center gap-3">
                                        <label className="text-sm font-medium">Rates differ based on meal type?</label>
                                        {ratesDifferLoading ? (
                                            <span className="text-gray-500">Checking...</span>
                                        ) : (
                                            <>
                                                <input
                                                    id="ratesDifferToggle"
                                                    type="checkbox"
                                                    checked={!!ratesDiffer}
                                                    onChange={(e) => setRatesDiffer(e.target.checked)}
                                                    className="h-4 w-4"
                                                />                                            
                                            </>
                                        )}
                                    </div>
                                    {ratesDifferError && <p className="text-xs text-red-600 mt-1">Error checking rates: {ratesDifferError}</p>}
                                    <div className="flex justify-end mt-4 gap-2">
                                        <button onClick={() => handleRefresh('mealTypes')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                        <button onClick={handleMealTypeEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Meal Types</button>
                                    </div>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'childPolicy':
                return (
                    <div className="bg-cyan-50 p-4 rounded-lg">
                        {editingChildPolicy ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {childPolicyList.map((range, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="number"
                                                value={range[0] || ''}
                                                onChange={(e) => handleChildPolicyChange(index, 'min', e.target.value)}
                                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Min age"
                                            />
                                            <span className="text-gray-500">-</span>
                                            <input
                                                type="number"
                                                value={range[1] || ''}
                                                onChange={(e) => handleChildPolicyChange(index, 'max', e.target.value)}
                                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Max age"
                                            />
                                            <span className="text-gray-500 text-sm">years</span>
                                            <button onClick={() => handleChildPolicyRemove(index)} className="text-red-500 hover:text-red-700">×</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input type="number" value={newChildPolicy.min} onChange={e => setNewChildPolicy({ ...newChildPolicy, min: e.target.value })} placeholder="Min Age" className="w-1/3 px-3 py-2 border rounded-lg" />
                                    <input type="number" value={newChildPolicy.max} onChange={e => setNewChildPolicy({ ...newChildPolicy, max: e.target.value })} placeholder="Max Age" className="w-1/3 px-3 py-2 border rounded-lg" />
                                    <button onClick={handleChildPolicyAdd} className="w-1/3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => {setEditingChildPolicy(false); setNewChildPolicy({min:'', max:''}); setChildPolicyList(Array.isArray(propertyAnalysis?.childPolicy?.child_policy) ? propertyAnalysis.childPolicy.child_policy : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleChildPolicySave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {childPolicyList && childPolicyList.length > 0 ? (
                                    <div>
                                        <div className="space-y-2">
                                            {childPolicyList.map((range, index) => {
                                                const rangeLabel = `Child Age Range ${index + 1}:`;
                                                const minAge = Array.isArray(range) ? range[0].toString() : 'N/A';
                                                const maxAge = Array.isArray(range) ? range[1].toString() : 'N/A';
                                                const separator = '-';
                                                const yearsSuffix = 'years';

                                                return (
                                                    <p key={index} className="flex items-center flex-wrap">
                                                        <strong onClick={() => handleItemClick(rangeLabel)} className="cursor-pointer hover:underline mr-1">{rangeLabel}</strong>
                                                        {renderSearchStatusIcon(rangeLabel)}
                                                        <span onClick={() => handleItemClick(minAge)} className="cursor-pointer hover:underline ml-1 mr-1">{minAge}</span>
                                                        {renderSearchStatusIcon(minAge)}
                                                        <span onClick={() => handleItemClick(separator)} className="cursor-pointer hover:underline ml-1 mr-1">{separator}</span>
                                                        {renderSearchStatusIcon(separator)}
                                                        <span onClick={() => handleItemClick(maxAge)} className="cursor-pointer hover:underline ml-1 mr-1">{maxAge}</span>
                                                        {renderSearchStatusIcon(maxAge)}
                                                        <span onClick={() => handleItemClick(yearsSuffix)} className="cursor-pointer hover:underline ml-1">{yearsSuffix}</span>
                                                        {renderSearchStatusIcon(yearsSuffix)}
                                                    </p>
                                                );
                                            })}
                                        </div>
                                    </div>
                                ) : ( <p className="text-gray-500">No specific child age ranges defined.</p> )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('childPolicy')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handleChildPolicyEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Child Policy</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'levies':
                return (
                    <div className="bg-purple-50 p-4 rounded-lg">
                        {editingLevies ? (
                            <React.Fragment>
                                <div className="space-y-4">
                                    {leviesList.map((levy, levyIndex) => (
                                        <div key={levyIndex} className="border border-gray-300 p-3 rounded-lg">
                                            <div className="flex items-center gap-2 mb-2">
                                                <input
                                                    type="text"
                                                    value={levy.type || ''}
                                                    onChange={(e) => handleLevyChange(levyIndex, 'type', e.target.value)}
                                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                                                    placeholder="Type"
                                                />
                                                <input
                                                    type="number"
                                                    value={levy.percentage || ''}
                                                    onChange={(e) => handleLevyChange(levyIndex, 'percentage', e.target.value)}
                                                    className="w-1/4 px-3 py-2 border border-gray-300 rounded-md"
                                                    placeholder="Percentage"
                                                />
                                                <select
                                                    value={levy.unit || 'room_per_night'}
                                                    onChange={(e) => handleLevyChange(levyIndex, 'unit', e.target.value)}
                                                    className="w-1/3 px-3 py-2 border border-gray-300 rounded-md"
                                                >
                                                    <option value="room_per_night">Room per night</option>
                                                    <option value="person_per_night">Person per night</option>
                                                    <option value="flat_fee">Flat fee</option>
                                                </select>
                                                <button onClick={() => handleLeviesRemove(levyIndex)} className="text-red-500 hover:text-red-700">×</button>
                                            </div>
                                            <div className="space-y-2">
                                                {levy.costs.map((cost, costIndex) => (
                                                    <div key={costIndex} className="flex items-center gap-2">
                                                        <input
                                                            type="number"
                                                            value={cost.amount || ''}
                                                            onChange={(e) => handleLevyCostChange(levyIndex, costIndex, 'amount', e.target.value)}
                                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                                                            placeholder="Cost Amount"
                                                        />
                                                        <input
                                                            type="text"
                                                            value={cost.description || ''}
                                                            onChange={(e) => handleLevyCostChange(levyIndex, costIndex, 'description', e.target.value)}
                                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                                                            placeholder="Cost Description"
                                                        />
                                                        <button onClick={() => removeCostFromLevy(levyIndex, costIndex)} className="text-red-500 hover:text-red-700 text-xs">Remove</button>
                                                    </div>
                                                ))}
                                            </div>
                                            <button onClick={() => addCostToLevy(levyIndex)} className="mt-2 text-sm text-blue-500 hover:text-blue-700">+ Add Cost</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="border border-gray-300 p-3 rounded-lg mt-4">
                                    <h4 className="font-semibold mb-2">Add New Levy</h4>
                                    <div className="flex items-center gap-2 mb-2">
                                        <input type="text" value={newLevy.type} onChange={e => setNewLevy({ ...newLevy, type: e.target.value })} placeholder="Type" className="flex-1 px-3 py-2 border rounded-lg" />
                                        <input type="number" value={newLevy.percentage} onChange={e => setNewLevy({ ...newLevy, percentage: e.target.value })} placeholder="Percentage" className="w-1/4 px-3 py-2 border rounded-lg" />
                                        <select
                                            value={newLevy.unit || 'room_per_night'}
                                            onChange={e => setNewLevy({ ...newLevy, unit: e.target.value })}
                                            className="w-1/3 px-3 py-2 border rounded-lg"
                                        >
                                            <option value="room_per_night">Room per night</option>
                                            <option value="person_per_night">Person per night</option>
                                            <option value="flat_fee">Flat fee</option>
                                        </select>
                                    </div>
                                    <div className="space-y-2">
                                        {newLevy.costs.map((cost, index) => (
                                            <div key={index} className="flex items-center gap-2">
                                                <input type="number" value={cost.amount} onChange={e => handleNewLevyCostChange(index, 'amount', e.target.value)} placeholder="Cost Amount" className="flex-1 px-3 py-2 border rounded-lg" />
                                                <input type="text" value={cost.description} onChange={e => handleNewLevyCostChange(index, 'description', e.target.value)} placeholder="Cost Description" className="flex-1 px-3 py-2 border rounded-lg" />
                                                <button onClick={() => removeCostFieldFromNewLevy(index)} className="text-red-500 hover:text-red-700 text-xs">Remove</button>
                                            </div>
                                        ))}
                                    </div>
                                    <button onClick={addNewCostFieldToNewLevy} className="mt-2 text-sm text-blue-500 hover:text-blue-700">+ Add Cost Field</button>
                                    <button onClick={handleLeviesAdd} className="mt-2 w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add Levy</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-4">
                                    <button onClick={() => {setEditingLevies(false); setNewLevy({type:'', percentage:'', unit:'room_per_night', costs:[{amount:'', description:''}]}); setLeviesList(Array.isArray(propertyAnalysis?.levies?.levies) ? propertyAnalysis.levies.levies : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleLeviesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {leviesList && leviesList.length > 0 ? (
                                    <div>
                                        <div className="space-y-4">
                                            {leviesList.map((levy, index) => {
                                                console.log("Levy", levy)
                                                const typeLabel = "Type:";
                                                const typeValue = levy.type ?? 'N/A';
                                                const percentageLabel = "Percentage:";
                                                const percentageRaw = levy.percentage ?? '';
                                                const percentageValue = (percentageRaw !== '' && percentageRaw !== undefined) ? `${percentageRaw}%` : 'N/A';
                                                const unitLabel = "Unit:";
                                                const unitValue = formatLevyUnit(levy.unit ?? 'room per night');

                                                return (
                                                    <div key={index} className="border-b border-purple-200 pb-3 last:border-b-0">
                                                        <div className="flex justify-between items-start">
                                                            <div>
                                                                <p className="flex items-center flex-wrap">
                                                                    <strong onClick={() => handleItemClick(typeLabel)} className="cursor-pointer hover:underline mr-1">{typeLabel}</strong>
                                                                    {renderSearchStatusIcon(typeLabel)}
                                                                    <span onClick={() => handleItemClick(typeValue)} className="cursor-pointer hover:underline ml-1">{typeValue}</span>
                                                                    {renderSearchStatusIcon(typeValue)}
                                                                </p>
                                                                <p className="flex items-center flex-wrap">
                                                                    <strong onClick={() => handleItemClick(percentageLabel)} className="cursor-pointer hover:underline mr-1">{percentageLabel}</strong>
                                                                    {renderSearchStatusIcon(percentageLabel)}
                                                                    <span onClick={() => handleItemClick(percentageValue)} className="cursor-pointer hover:underline ml-1">{percentageValue}</span>
                                                                    {renderSearchStatusIcon(percentageValue)}
                                                                </p>
                                                                <p className="flex items-center flex-wrap">
                                                                    <strong onClick={() => handleItemClick(unitLabel)} className="cursor-pointer hover:underline mr-1">{unitLabel}</strong>
                                                                    {renderSearchStatusIcon(unitLabel)}
                                                                    <span onClick={() => handleItemClick(unitValue)} className="cursor-pointer hover:underline ml-1">{unitValue}</span>
                                                                    {renderSearchStatusIcon(unitValue)}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        {levy.costs && levy.costs.length > 0 && (
                                                            <div className="mt-2 pl-4">
                                                                <strong className="text-sm">Costs:</strong>
                                                                <ul className="list-disc list-inside space-y-1 mt-1">
                                                                    {levy.costs.map((cost, costIndex) => (
                                                                        <li key={costIndex}>
                                                                            <span onClick={() => handleItemClick(cost.amount?.toString())} className="cursor-pointer hover:underline">{cost.amount}</span>
                                                                            {cost.description && (
                                                                                <span onClick={() => handleItemClick(cost.description)} className="text-gray-600 ml-2 cursor-pointer hover:underline">({cost.description})</span>
                                                                            )}
                                                                        </li>
                                                                    ))}
                                                                </ul>
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                ) : ( <p className="text-gray-500">No levies found for this property.</p> )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('levies')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handleLeviesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Levies</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'includes':
                return (
                    <div className="bg-green-50 p-4 rounded-lg">
                        {editingIncludes ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {includesList.map((item, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={item}
                                                onChange={(e) => handleIncludeChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter include item"
                                            />
                                            <button onClick={() => handleIncludesRemove(item)} className="text-red-500 hover:text-red-700">×</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input type="text" value={newInclude} onChange={e => setNewInclude(e.target.value)} onKeyPress={handleIncludesKeyPress} placeholder="Add new include" className="flex-grow px-3 py-2 border rounded-lg" />
                                    <button onClick={handleIncludesAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => {setEditingIncludes(false); setNewInclude(''); setIncludesList(Array.isArray(propertyAnalysis?.includes) ? propertyAnalysis.includes : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleIncludesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {includesList && includesList.length > 0 ? (
                                    <div>
                                        <ul className="list-disc list-inside space-y-1">
                                            {includesList.map((item, index) => (
                                                <li key={index} className="flex items-center">
                                                    <span onClick={() => handleItemClick(item)} className="cursor-pointer hover:underline">
                                                        {item}
                                                    </span>
                                                    {renderSearchStatusIcon(item)}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                ) : ( <p className="text-gray-500">No includes found</p> )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('includes')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handleIncludesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Includes</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            case 'excludes':
                return (
                    <div className="bg-red-50 p-4 rounded-lg">
                        {editingExcludes ? (
                            <React.Fragment>
                                <div className="space-y-2">
                                    {excludesList.map((item, index) => (
                                        <div key={index} className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={item}
                                                onChange={(e) => handleExcludeChange(index, e.target.value)}
                                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                                placeholder="Enter exclude item"
                                            />
                                            <button onClick={() => handleExcludesRemove(item)} className="text-red-500 hover:text-red-700">×</button>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <input type="text" value={newExclude} onChange={e => setNewExclude(e.target.value)} onKeyPress={handleExcludesKeyPress} placeholder="Add new exclude" className="flex-grow px-3 py-2 border rounded-lg" />
                                    <button onClick={handleExcludesAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                </div>
                                <div className="flex justify-end gap-2 mt-2">
                                    <button onClick={() => {setEditingExcludes(false); setNewExclude(''); setExcludesList(Array.isArray(propertyAnalysis?.excludes) ? propertyAnalysis.excludes : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button onClick={handleExcludesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {(excludesList && excludesList.length > 0) ? (
                                    <ul className="list-disc list-inside space-y-1">
                                        {excludesList.map((item, index) => (
                                            <li key={index} className="flex items-center">
                                                <span onClick={() => handleItemClick(item)} className="cursor-pointer hover:underline">
                                                    {item}
                                                </span>
                                                {renderSearchStatusIcon(item)}
                                            </li>
                                        ))}
                                    </ul>
                                ) : (
                                    <p className="text-gray-500">No excludes found.</p>
                                )}
                                <div className="flex justify-end mt-4 gap-2">
                                    <button onClick={() => handleRefresh('excludes')} className="px-4 py-2 text-blue-500 hover:text-blue-700">Refresh</button>
                                    <button onClick={handleExcludesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Excludes</button>
                                </div>
                            </React.Fragment>
                        )}
                    </div>
                );
            default:
                return <p>Unknown section: {endpointName}</p>;
        }
    };


    return (
        <div className="min-h-screen bg-gray-100 py-6">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-3xl font-bold">Property Analysis</h1>
                    <button
                        onClick={handlePropertyAnalysisBackNavigation}
                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                    >
                        ← Back
                    </button>
                </div>
                
                {error && (
                    <div className="mb-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}
                
                <div className="mb-3">
                    <label htmlFor="modelSelect" className="block text-sm font-medium text-gray-700 mb-1">Select Model:</label>
                    <select 
                        id="modelSelect" 
                        className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        value={selectedModel} 
                        onChange={(e) => {
                            setSelectedModel(e.target.value);
                            localStorage.setItem('selectedModel', e.target.value);
                            // Optionally re-fetch analysis if model changes and a property is selected
                            // if (selectedProperty && workflowState) {
                            //     fetchPropertyAnalysis(selectedProperty, workflowState);
                            // }
                        }}
                    >
                        {modelOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-start"> {/* Changed items-stretch to items-start */}
                    <div className="lg:col-span-2 flex flex-col gap-6"> {/* Removed h-full */}
                        <div className="bg-white p-6 rounded-lg shadow-md flex-grow">
                            {selectedProperty && workflowState && (
                                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h2 className="text-xl font-semibold text-blue-800">
                                        Analyzing Property {workflowState.currentPropertyIndex + 1} / {workflowState.totalProperties}:
                                        <span 
                                            onClick={() => handleItemClick(selectedProperty)} 
                                            className="ml-2 font-bold cursor-pointer hover:underline"
                                        >
                                            {selectedProperty}
                                        </span>
                                        {renderSearchStatusIcon(selectedProperty)}
                                    </h2>
                                </div>
                            )}
                            <h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
                            
                            {!selectedProperty && Object.keys(endpointStatus).length === 0 && (
                                <p className="text-gray-500">Select a property or start workflow to view its analysis.</p>
                            )}
                            
                            {Object.keys(endpointStatus).length > 0 && propertyAnalysis && (
                                <div className="space-y-4">
                                    {Object.keys(endpointMap).map((endpointName) => {
                                        const { status, label, message } = endpointStatus[endpointName] || {};
                                        if (!status || endpointName === 'validation') return null;

                                        const colors = {
                                            periods: 'text-indigo-600',
                                            roomTypes: 'text-purple-600',
                                            mealTypes: 'text-orange-600',
                                            childPolicy: 'text-cyan-600',
                                            levies: 'text-purple-600',
                                            includes: 'text-green-600',
                                            excludes: 'text-red-600',
                                        };

                                        return (
                                            <div key={endpointName}>
                                                <div className="flex justify-between items-center mb-2">
                                                    <h3 onClick={() => handleItemClick(label)} className={`text-lg font-medium ${colors[endpointName] || 'text-gray-800'} cursor-pointer hover:underline`}>{label}</h3>
                                                    <StatusIndicator status={status} />
                                                </div>
                                                {status === 'completed' && renderAnalysisSection(endpointName)}
                                                {status === 'loading' && <div className="bg-gray-50 p-4 rounded-lg text-center text-gray-500">Loading...</div>}
                                                {status === 'error' && <div className="bg-red-50 p-3 rounded-md text-red-600">Error: {message}</div>}
                                            </div>
                                        );
                                    })}

                                    <div className="mt-4">
                                        <button
                                            onClick={handleRoomTypeAnalysis}
                                            className="w-full px-4 py-3 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600"
                                            disabled={Object.values(endpointStatus).some(s => s.status === 'loading')}
                                        >
                                            Process Room Types
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    
                    <div className="lg:col-span-3 bg-white p-6 rounded-lg shadow-md sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto"> {/* Make PDF viewer sticky */}
                        <h2 className="text-xl font-semibold mb-4">PDF Viewer</h2>
                        {pdfUrl ? <EnhancedPdfViewer url={pdfUrl} searchText={searchText} searchStatus={searchStatus} onSearchResult={handleSearchResult} /> : <p>No PDF loaded.</p>}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default PropertyAnalysis;
