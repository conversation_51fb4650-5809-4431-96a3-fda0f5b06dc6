// Timeout configuration for the application
export const TIMEOUTS = {
    // PDF upload and processing timeout (10 minutes)
    PDF_UPLOAD: 600000,
    
    // General API request timeout (2 minutes)
    API_REQUEST: 120000,
    
    // Auto-processing polling interval (5 seconds)
    POLLING_INTERVAL: 5000,
    
    // File download timeout (5 minutes)
    FILE_DOWNLOAD: 300000
};

// Error messages for different timeout scenarios
export const TIMEOUT_MESSAGES = {
    PDF_UPLOAD: 'Upload timeout: The PDF processing took longer than 10 minutes. This can happen with very large or complex documents. Please try again or contact support if the issue persists.',
    API_REQUEST: 'Request timeout: The operation took longer than expected. Please try again.',
    GENERAL: 'Operation timeout: Please try again or contact support if the issue persists.'
};

// HTTP status code messages
export const HTTP_ERROR_MESSAGES = {
    502: 'Server temporarily unavailable. Please try again in a few moments.',
    503: 'Server temporarily unavailable. Please try again in a few moments.',
    504: 'Gateway timeout: The server took too long to respond. This can happen with large or complex documents. Please try again or contact support if the issue persists.'
};
