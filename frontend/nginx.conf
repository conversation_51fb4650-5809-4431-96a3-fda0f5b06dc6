events {
    worker_connections 1024;
}

http {
    resolver 127.0.0.11 valid=30s;
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 50M;
    sendfile      on;
    keepalive_timeout 65;

    server {
        listen 400;
        server_name localhost;

        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
            # For index.html, we want to ensure clients re-validate
            # This is better handled by a specific location block for /index.html
            # or by ensuring index.html itself is not cached by downstream rules.
            # The try_files directive means this block will often serve index.html.
        }

        # Specific handling for index.html to prevent caching
        location = /index.html {
            expires -1;
            add_header Cache-Control 'no-cache, must-revalidate';
        }

        # Cache static assets aggressively - js and css
        # Assumes assets are fingerprinted or have versioning in their names
        location ~* \.(?:css|js)$ {
            expires 7d;
            add_header Cache-Control "public";
        }

        # For other static files like images, fonts, etc.
        location ~* \.(?:jpg|jpeg|gif|png|ico|svg|woff|woff2|ttf|eot)$ {
            expires 7d;
            add_header Cache-Control "public";
        }

        # JSON files and other data files should not be cached or have short cache times
        location ~* \.(?:json|xml)$ {
            expires -1;
            add_header Cache-Control 'no-cache, must-revalidate';
        }

        # API proxy
        location /api/ {
            rewrite ^/api(/.*)$ $1 break;
            proxy_pass http://backend:6060/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Increase timeouts for long-running operations like PDF processing
            proxy_connect_timeout 600s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
        }

        # CORS configuration - kept from original
        # Applied globally within the server block now.
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Accept,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

        # if ($request_method = 'OPTIONS') {
        #     # Custom headers for OPTIONS handling should be within this block only if needed
        #     # and not globally if they conflict. The global ones above should suffice.
        #     add_header 'Access-Control-Allow-Origin' '*';
        #     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS'; # Redundant if global already set
        #     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Accept,Authorization'; # Redundant
        #     add_header 'Access-Control-Max-Age' 1728000;
        #     add_header 'Content-Type' 'text/plain; charset=utf-8';
        #     add_header 'Content-Length' 0;
        #     return 204;
        # }
    }
}
